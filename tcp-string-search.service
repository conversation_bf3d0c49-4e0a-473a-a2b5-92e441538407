[Unit]
Description=TCP String Search Server
Documentation=file:///home/<USER>/.local/tcp-string-search/README.md
After=network.target
Wants=network.target

[Service]
Type=simple
User=tcp-search
Group=tcp-search
WorkingDirectory=/home/<USER>/.local/tcp-string-search
ExecStart=/usr/bin/python3 /home/<USER>/.local/tcp-string-search/server.py
ExecReload=/bin/kill -HUP $MAINPID
Restart=always
RestartSec=5
StandardOutput=journal
StandardError=journal
SyslogIdentifier=tcp-string-search

# Security settings
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/home/<USER>/.local/tcp-string-search/logs
CapabilityBoundingSet=CAP_NET_BIND_SERVICE
AmbientCapabilities=CAP_NET_BIND_SERVICE

# Resource limits
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
