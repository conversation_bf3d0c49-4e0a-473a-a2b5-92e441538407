# Search Algorithm Performance Report

## Performance Summary

### Average Search Time by File Size

| Algorithm                     |   10K |    50K |   100K |   250K |
|:------------------------------|------:|-------:|-------:|-------:|
| Binary Search (Deduplicated)  | 0.003 |  0.003 |  0.003 |  0.002 |
| Hash Set (FrozenSet)          | 0.001 |  0.001 |  0.001 |  0.001 |
| Hash Set (FrozenSet) (Reread) | 0.022 |  0.023 |  0.018 |  0.028 |
| Linear Search (Optimized)     | 1.63  |  7.671 | 10.363 | 20.248 |
| Memory-Mapped                 | 1.492 | 10.675 | 12.134 | 22.107 |
| Native Grep                   | 2.813 |  2.981 |  3.592 |  5.833 |

### Load Time by File Size

| Algorithm                     |   10K |    50K |   100K |    250K |
|:------------------------------|------:|-------:|-------:|--------:|
| Binary Search (Deduplicated)  | 7.753 | 27.087 | 73.074 | 129.258 |
| Hash Set (FrozenSet)          | 2.037 | 14.7   | 33.156 |  77.032 |
| Hash Set (FrozenSet) (Reread) | 2.521 | 14.506 | 32.018 |  75.311 |
| Linear Search (Optimized)     | 0.082 |  0.045 |  0.043 |   0.046 |
| Memory-Mapped                 | 0.071 |  0.113 |  0.165 |   0.2   |
| Native Grep                   | 0.07  |  0.066 |  0.063 |   0.057 |

## Performance Visualizations

### Load Time Comparison
![Load Time Comparison](load_time_(ms)_chart.png)

### Search Time Comparison
![Search Time Comparison](avg_search_time_(ms)_chart.png)

## Algorithm Characteristics

1. **HashSet (FrozenSet)**: O(1) lookup with memory trade-off
2. **HashSet (FrozenSet) (Reread)**: No initial memory overhead,                but slower search
3. **Linear Search**: Simple implementation,                 high time complexity (O(n))
4. **Binary Search**: O(log n) search with sorting overhead
5. **Memory-Mapped**: Efficient for large files
6. **Native Grep**: System-level optimization
