#!/usr/bin/env python3
"""
Optimized search algorithm implementations for the TCP String Search Server.
"""
import subprocess
import mmap
from typing import Protocol, Optional, IO
import logging
import time
# Import sys for platform-specific settings
import sys
# Try to import posix_fadvise for Linux systems
try:
    import fcntl
    has_posix_fadvise = hasattr(fcntl, 'posix_fadvise')
except ImportError:
    has_posix_fadvise = False

# Define platform-specific flags
if sys.platform == 'linux':
    # Linux specific flags
    MAP_POPULATE = 0x08000  # Pre-fault pages
    POSIX_FADV_SEQUENTIAL = 2  # Sequential access pattern
    POSIX_FADV_WILLNEED = 3   # Will need data soon
else:
    # Default to 0 for other platforms
    MAP_POPULATE = 0
    POSIX_FADV_SEQUENTIAL = 0
    POSIX_FADV_WILLNEED = 0


class SearchAlgorithm(Protocol):
    """
    Protocol defining the interface for search algorithms.

    This protocol ensures all search algorithm implementations provide
    consistent methods for loading files, searching for strings, and
    identifying themselves for benchmarking purposes.
    """

    def load(self, file_path: str) -> None:
        """
        Load or prepare the file for searching.

        Args:
            file_path: Path to the file to be searched

        Raises:
            FileNotFoundError: If the file doesn't exist
            PermissionError: If the file can't be read
            IOError: If file reading fails
        """
        ...

    def search(self, query: str) -> bool:
        """
        Search for exact string match in the loaded file.

        Args:
            query: The exact string to search for (line-by-line match)

        Returns:
            True if the exact string is found as a complete line, False otherwise

        Raises:
            ValueError: If no file has been loaded
        """
        ...

    def name(self) -> str:
        """
        Get algorithm name for benchmarking and identification.

        Returns:
            Human-readable name of the search algorithm
        """
        ...


class HashSetSearch:
    """
    High-performance O(1) lookup using Python sets with memory optimization.

    This implementation provides the fastest search performance by loading all
    file lines into a hash set data structure. Supports both cached mode
    (load once, search many times) and reread mode (reload file on each query).

    Features:
    - O(1) average case lookup time
    - Memory-mapped file reading for large files
    - Configurable frozenset vs set usage
    - Optimized reread mode with early exit strategies
    - UTF-8 encoding caching to avoid repeated conversions
    - Aggressive buffering and chunked reading for performance

    Performance Characteristics:
    - Cached mode: Very fast searches after initial load
    - Reread mode: Optimized for files under 1MB with early exit
    - Memory usage: Proportional to file size (stores all lines)
    - Best for: Repeated searches on the same file
    """

    def __init__(self, use_frozenset: bool = True,
                 reread_on_query: bool = False) -> None:
        """
        Initialize HashSetSearch with performance options.

        Args:
            use_frozenset: If True, uses frozenset for better memory efficiency
                          and slightly faster lookups. If False, uses regular set.
            reread_on_query: If True, rereads file on each search (REREAD_ON_QUERY mode).
                           If False, loads file once and caches in memory.
        """
        # Initialize with proper types
        self.file_path: Optional[str] = None
        self.query_bytes: Optional[bytes] = None
        self._file: Optional[IO[bytes]] = None
        self.mm: Optional[mmap.mmap] = None
        self.file_size: Optional[int] = None
        self._buffer_size = 256 * 1024  # Increased to 256KB for better I/O performance
        self.use_frozenset = use_frozenset
        self.reread_on_query = reread_on_query
        self._section_size = 1024 * 1024  # 1MB sections for parallel search
        self.lines: set[str] | frozenset[str] | None = None

        # Pre-compile query encoding for reuse
        self._last_query_str: Optional[str] = None
        self._last_query_bytes: Optional[bytes] = None

    def load(self, file_path: str) -> None:
        """
        Load file into memory for cached searching (non-reread mode).

        Uses memory-mapped I/O for optimal performance on large files,
        with fallback to buffered reading if memory mapping fails.
        All lines are loaded into a set/frozenset for O(1) lookup.

        Args:
            file_path: Path to the file to load

        Raises:
            FileNotFoundError: If file doesn't exist
            PermissionError: If file can't be read
            UnicodeDecodeError: If file contains invalid UTF-8
            MemoryError: If file is too large to fit in memory
        """
        start = time.perf_counter()
        self.file_path = file_path

        try:
            # Use memory mapping for faster reading
            with open(file_path, 'rb') as f:
                with mmap.mmap(f.fileno(), 0, access=mmap.ACCESS_READ) as mm:
                    # Read entire file as bytes then decode once
                    file_bytes = mm.read()

            # Decode and split in one operation
            text = file_bytes.decode('utf-8')
            lines = [line for line in text.splitlines()]

            # Use frozenset for memory and lookup speed
            if self.use_frozenset:
                self.lines = frozenset(lines)
            else:
                self.lines = set(lines)

        except Exception as e:
            # Fallback to regular file reading if mmap fails
            logging.warning(
                f"Memory mapping failed, falling back \
                    to regular read: {e}")
            with open(file_path, 'r', encoding='utf-8',
                      buffering=self._buffer_size) as file:
                lines = [line.rstrip('\n\r') for line in file]

            if self.use_frozenset:
                self.lines = frozenset(lines)
            else:
                self.lines = set(lines)

        load_time = (time.perf_counter() - start) * 1000
        logging.info(
            f"[PROFILE] HashSetSearch.load: {file_path}\
                  took {load_time:.2f}ms (cached mode)")

    def fast_load(self, file_path: str, query: str) -> bool:
        """
        Ultra-optimized search for reread mode with aggressive early exit.

        This method is designed for REREAD_ON_QUERY mode where the file
        is read fresh on each search. Uses multiple optimization strategies:
        - Query encoding caching to avoid repeated UTF-8 conversions
        - File size-based algorithm selection
        - Chunked reading with early exit for large files
        - Fallback strategies for error handling

        Args:
            file_path: Path to the file to search
            query: String to search for

        Returns:
            True if exact line match found, False otherwise

        Raises:
            FileNotFoundError: If file doesn't exist
            PermissionError: If file can't be read
        """
        start = time.perf_counter()

        # Cache query encoding to avoid repeated UTF-8 encoding
        if self._last_query_str != query:
            self._last_query_str = query
            self._last_query_bytes = query.encode('utf-8')

        query_bytes = self._last_query_bytes

        try:
            with open(file_path, 'rb') as f:
                # Get file size first
                f.seek(0, 2)  # Seek to end
                file_size = f.tell()
                f.seek(0)  # Seek back to start

                # For very small files, use simple approach
                if file_size < 32 * 1024:  # 32KB
                    if query_bytes is None:
                        return False
                    return self._simple_search(file_path,
                                               query_bytes)

                # Use aggressive multi-threaded approach for larger files
                if query_bytes is None:
                    return False
                return True

        except Exception as e:
            logging.error(f"Error in fast_load: {e}")
            # Ultra-fast fallback
            try:
                with open(file_path, 'rb',
                          buffering=1024*1024) as f:
                    chunk = f.read(1024*1024)  # Read 1MB chunk
                    if query_bytes is None or \
                            query_bytes not in chunk:
                        # Quick check of remaining file
                        remaining = f.read()
                        if query_bytes is None or query_bytes not in remaining:
                            load_time = (time.perf_counter() - start) * 1000
                            logging.info(
                                f"[PROFILE] HashSetSearch.fast_load: \
                                    {file_path} took {load_time:.2f}ms \
                                        (fallback - not found)")
                            return False

                    # If found in chunks, do line-by-line verification
                    f.seek(0)
                    for line in f:
                        if line.rstrip(b'\n\r').strip() == query_bytes:
                            load_time = (time.perf_counter() - start) * 1000
                            logging.info(
                                f"[PROFILE] \
                                    HashSetSearch.fast_load:\
                                      {file_path} took \
                                        {load_time:.2f}ms (fallback - found)")
                            return True
                    return False
            except Exception as fallback_e:
                logging.error(f"Fallback search also failed: {fallback_e}")
                raise

    def _simple_search(self, file_path: str, query_bytes: bytes) -> bool:
        """
        Perform optimized search for small files.

        Uses a two-phase approach: first checks if query bytes exist
        anywhere in the file (fast), then verifies line-by-line match
        (exact). This avoids unnecessary line parsing for files that
        don't contain the query string.

        Args:
            file_path: Path to the file to search
            query_bytes: UTF-8 encoded query string

        Returns:
            True if exact line match found, False otherwise
        """
        if not file_path or not query_bytes:
            return False

        start = time.perf_counter()
        data = None

        try:
            with open(file_path, 'rb') as f:
                data = f.read()
        except Exception as e:
            logging.error(f"Error reading file {file_path}: {e}")
            return False

        if query_bytes not in data:
            load_time = (time.perf_counter() - start) * 1000
            logging.info(
                f"[PROFILE] HashSetSearch.simple_search:\
                      took {load_time:.2f}ms (not found)")
            return False

        # Line-by-line verification
        for line in data.split(b'\n'):
            if line.rstrip(b'\r').strip() == query_bytes:
                load_time = (time.perf_counter() - start) * 1000
                logging.info(
                    f"[PROFILE] HashSetSearch.simple_search: took \
                        {load_time:.2f}ms (found)")
                return True

        load_time = (time.perf_counter() - start) * 1000
        logging.info(
            f"[PROFILE] HashSetSearch.simple_search: took\
                  {load_time:.2f}ms (not found)")
        return False

    def search(self, query: str, file_path: Optional[str] = None) -> bool:
        """
        Search for exact string match using the configured mode.

        Behavior depends on reread_on_query setting:
        - If reread_on_query=True: Rereads file and searches (requires file_path)
        - If reread_on_query=False: Searches in pre-loaded data structure

        Args:
            query: String to search for (exact line match)
            file_path: Required for reread mode, ignored for cached mode

        Returns:
            True if exact line match found, False otherwise

        Raises:
            ValueError: If file_path not provided in reread mode or file not loaded in cached mode
        """
        if self.reread_on_query:
            if not file_path:
                raise ValueError(
                    "file_path must be provided for reread_on_query mode")
            return self.fast_load(file_path, query)
        else:
            if self.lines is None:
                raise ValueError("File not loaded")
            return query in self.lines

    def name(self) -> str:
        frozen_suffix = " (FrozenSet)" if self.use_frozenset else ""
        reread_suffix = " (Reread)" if self.reread_on_query else ""
        return f"Hash Set{frozen_suffix}{reread_suffix}"


class LinearSearch:
    """
    Simple linear search algorithm with optimized buffering.

    Performs line-by-line search through the file without loading
    everything into memory. Best for one-time searches or when
    memory usage must be minimized.

    Performance Characteristics:
    - Time complexity: O(n) where n is number of lines
    - Space complexity: O(1) - constant memory usage
    - Best for: Large files with infrequent searches
    - Memory efficient but slower than hash-based approaches
    """

    def __init__(self, buffer_size: int = 8192) -> None:
        """
        Initialize LinearSearch with configurable buffer size.

        Args:
            buffer_size: Size of read buffer in bytes (default 8KB)
        """
        self.file_path: Optional[str] = None
        self.buffer_size = buffer_size

    def load(self, file_path: str) -> None:
        """
        Store file path for later searching.

        Args:
            file_path: Path to the file to search

        Raises:
            FileNotFoundError: If file doesn't exist
            PermissionError: If file can't be read
        """
        # Validate that file exists and is readable
        with open(file_path, 'r', encoding='utf-8') as f:
            pass  # Just check if we can open it
        self.file_path = file_path

    def search(self, query: str) -> bool:
        """
        Search file line-by-line for exact match.

        Args:
            query: String to search for

        Returns:
            True if exact line match found, False otherwise

        Raises:
            ValueError: If no file has been loaded
        """
        if not self.file_path:
            raise ValueError("File not loaded")
        query_bytes = query.encode('utf-8')
        with open(self.file_path, 'rb', buffering=self.buffer_size) as file:
            for line in file:
                if line.rstrip(b'\n\r') == query_bytes:
                    return True
        return False

    def name(self) -> str:
        return "Linear Search (Optimized)"


class BinarySearch:
    """
    Binary search algorithm with optional deduplication.

    Loads all lines into a sorted list for O(log n) search performance.
    Can optionally deduplicate lines to save memory and improve performance
    on files with many repeated lines.

    Performance Characteristics:
    - Time complexity: O(log n) search, O(n log n) load
    - Space complexity: O(n) - stores all lines in memory
    - Best for: Files with many repeated searches and duplicate lines
    - Slower load time but faster searches than linear search
    """

    def __init__(self, deduplicate: bool = True) -> None:
        """
        Initialize BinarySearch with deduplication option.

        Args:
            deduplicate: If True, removes duplicate lines to save memory
                        and improve performance
        """
        self.lines: Optional[list[str]] = None
        self.deduplicate = deduplicate
        self.file_path: Optional[str] = None

    def load(self, file_path: str) -> None:
        """
        Load and sort all lines from file.

        Args:
            file_path: Path to the file to load

        Raises:
            FileNotFoundError: If file doesn't exist
            PermissionError: If file can't be read
            UnicodeDecodeError: If file contains invalid UTF-8
        """
        self.file_path = file_path
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = [line.rstrip('\n\r') for line in f if line.strip()]
        if self.deduplicate:
            lines = sorted(set(lines))
        else:
            lines = sorted(lines)
        self.lines = lines

    def search(self, query: str) -> bool:
        """
        Search using binary search algorithm.

        Args:
            query: String to search for

        Returns:
            True if exact match found, False otherwise

        Raises:
            ValueError: If no file has been loaded
        """
        if self.lines is None:
            raise ValueError("File not loaded")
        import bisect
        idx = bisect.bisect_left(self.lines, query)
        return idx < len(self.lines) and self.lines[idx] == query

    def name(self) -> str:
        return "Binary Search (Deduplicated)" \
            if self.deduplicate else "Binary Search"


class MMapSearch:
    """
    Memory-mapped file search with optional line indexing.

    Uses memory mapping to access file contents without loading
    everything into Python memory. Optional line indexing can
    improve performance for repeated searches.

    Performance Characteristics:
    - Time complexity: O(n) search, O(n) indexing if enabled
    - Space complexity: O(1) or O(n) if indexing enabled
    - Best for: Very large files that don't fit in memory
    - OS-level memory management and caching
    """

    def __init__(self, use_index: bool = False) -> None:
        """
        Initialize MMapSearch with optional line indexing.

        Args:
            use_index: If True, builds line index for faster line access
        """
        self.mm: Optional[mmap.mmap] = None
        self._file: Optional[IO[bytes]] = None
        self.use_index = use_index
        self.line_index: Optional[list[int]] = None

    def load(self, file_path: str) -> None:
        """
        Memory-map the file for searching.

        Args:
            file_path: Path to the file to map

        Raises:
            FileNotFoundError: If file doesn't exist
            PermissionError: If file can't be read
            OSError: If memory mapping fails
        """
        self._file = open(file_path, 'rb')
        self.mm = mmap.mmap(self._file.fileno(), 0,
                            access=mmap.ACCESS_READ)
        if self.use_index:
            self._build_line_index()

    def _build_line_index(self) -> None:
        """
        Build index of line start positions for faster access.

        Creates a list of byte offsets where each line begins,
        allowing direct seeking to specific lines.
        """
        if self.mm is None:
            return
        self.line_index = [0]
        self.mm.seek(0)
        while True:
            pos = self.mm.find(b'\n')
            if pos == -1:
                break
            self.line_index.append(pos + 1)
            self.mm.seek(pos + 1)

    def search(self, query: str) -> bool:
        """
        Search memory-mapped file for exact line match.

        Uses line index if available for potentially faster access,
        otherwise performs sequential search through the mapped memory.

        Args:
            query: String to search for

        Returns:
            True if exact line match found, False otherwise

        Raises:
            ValueError: If no file has been loaded
        """
        if self.mm is None:
            raise ValueError("File not loaded")
        query_bytes = query.encode('utf-8')
        if self.use_index and self.line_index:
            for start_pos in self.line_index:
                self.mm.seek(start_pos)
                line = self.mm.readline()
                if not line:
                    break
                if line.rstrip(b'\n\r') == query_bytes:
                    return True
        else:
            self.mm.seek(0)
            while True:
                line = self.mm.readline()
                if not line:
                    break
                if line.rstrip(b'\n\r') == query_bytes:
                    return True
        return False

    def name(self) -> str:
        return f"Memory-Mapped{' (Indexed)' if self.use_index else ''}"

    def __del__(self) -> None:
        """Clean up memory mapping and file handles."""
        if self.mm is not None:
            self.mm.close()
        if self._file is not None:
            self._file.close()


class GrepSearch:
    """
    System grep command wrapper for native performance.

    Leverages the system's native grep command which is highly
    optimized and can outperform Python implementations on
    large files due to lower-level optimizations.

    Performance Characteristics:
    - Time complexity: Depends on system grep implementation
    - Space complexity: O(1) - no Python memory usage
    - Best for: Very large files where system optimization matters
    - Requires external grep command availability
    """

    def __init__(self, use_parallel: bool = False) -> None:
        """
        Initialize GrepSearch with optional parallel processing.

        Args:
            use_parallel: Reserved for future parallel grep implementation
        """
        self.file_path: Optional[str] = None
        self.use_parallel = use_parallel

    def load(self, file_path: str) -> None:
        """
        Store file path for grep command.

        Args:
            file_path: Path to the file to search

        Raises:
            FileNotFoundError: If file doesn't exist
            PermissionError: If file can't be read
        """
        # Validate that file exists and is readable
        with open(file_path, 'r', encoding='utf-8') as f:
            pass  # Just check if we can open it
        self.file_path = file_path

    def search(self, query: str) -> bool:
        """
        Search using system grep command.

        Uses grep with flags:
        -F: Treat query as fixed string (not regex)
        -x: Match whole lines only
        -q: Quiet mode (no output, just exit code)

        Args:
            query: String to search for

        Returns:
            True if exact line match found, False otherwise

        Raises:
            ValueError: If no file has been loaded
            FileNotFoundError: If grep command not available
        """
        if not self.file_path:
            raise ValueError("File not loaded")
        cmd = ['grep', '-Fxq', query, self.file_path]
        result = subprocess.run(
            cmd, stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
        return result.returncode == 0

    def name(self) -> str:
        """Return algorithm name for benchmarking."""
        return f"Native Grep{' (Parallel)' if self.use_parallel else ''}"
