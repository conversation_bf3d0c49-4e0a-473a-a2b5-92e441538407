# TCP String Search Server - Complete Dependencies
# Author: <PERSON>
# Date: 2025
#
# This file contains all external dependencies used across the entire codebase.
# Standard library modules are listed for documentation but don't need installation.

# ============================================================================
# CORE EXTERNAL DEPENDENCIES (Required for installation)
# ============================================================================

# Testing and code quality
pytest>=7.4.0,<8.0.0              # Unit testing framework
pytest-cov>=4.1.0,<5.0.0          # Coverage reporting for pytest
pytest-mock>=3.11.1,<4.0.0        # Mock object library for pytest
mypy>=1.5.0,<2.0.0                 # Static type checker

# Performance monitoring and system information
psutil>=5.9.0,<6.0.0               # System and process utilities

# Data visualization and analysis
matplotlib>=3.7.0,<4.0.0           # Plotting library for performance charts
pandas>=2.0.0,<3.0.0               # Data analysis and manipulation
numpy>=1.24.0,<2.0.0               # Numerical computing (pandas dependency)

# PDF report generation
reportlab>=4.0.0,<5.0.0            # PDF generation library
pillow>=10.0.0,<11.0.0             # Image processing (reportlab dependency)

# SSL and cryptographic operations
cryptography>=41.0.0,<42.0.0       # Cryptographic recipes and primitives

# Configuration file handling
pyyaml>=6.0,<7.0                   # YAML parser and emitter

# ============================================================================
# PYTHON STANDARD LIBRARY MODULES (Built-in, no installation needed)
# ============================================================================
# These modules are part of Python's standard library and are documented
# here for completeness. They are used throughout the codebase but don't
# require separate installation.

# Core system and I/O modules:
# - argparse          # Command-line argument parsing
# - bisect            # Binary search algorithms
# - configparser      # Configuration file parser (INI format)
# - datetime          # Date and time handling
# - json              # JSON encoder and decoder
# - logging           # Flexible event logging system
# - mmap              # Memory-mapped file support
# - os                # Operating system interface
# - pathlib           # Object-oriented filesystem paths
# - random            # Generate random numbers
# - shutil            # High-level file operations
# - socket            # Low-level networking interface
# - ssl               # TLS/SSL wrapper for socket objects
# - statistics        # Mathematical statistics functions
# - subprocess        # Subprocess management
# - sys               # System-specific parameters and functions
# - tempfile          # Generate temporary files and directories
# - threading         # Thread-based parallelism
# - time              # Time access and conversions
# - typing            # Support for type hints
# - unittest          # Unit testing framework
# - fcntl             # File control and I/O control (Unix/Linux only)
# - signal            # Set handlers for asynchronous events
# - struct            # Interpret bytes as packed binary data
# - collections       # Specialized container datatypes
# - itertools         # Functions creating iterators for efficient looping
# - functools         # Higher-order functions and operations on callable objects
# - contextlib        # Utilities for with-statement contexts
# - traceback         # Print or retrieve a stack traceback
# - warnings          # Warning control
# - weakref           # Weak references
# - copy              # Shallow and deep copy operations
# - pickle            # Python object serialization
# - base64            # Base16, Base32, Base64, Base85 data encodings
# - hashlib           # Secure hash and message digest algorithms
# - hmac              # Keyed-Hashing for Message Authentication
# - secrets           # Generate cryptographically strong random numbers
# - uuid              # UUID objects according to RFC 4122
# - re                # Regular expression operations
# - string            # Common string operations
# - textwrap          # Text wrapping and filling
# - difflib           # Helpers for computing deltas
# - pprint            # Data pretty printer
# - reprlib           # Alternate repr() implementation
# - enum              # Support for enumerations
# - dataclasses       # Data classes
# - abc               # Abstract base classes
# - inspect           # Inspect live objects
# - importlib         # The implementation of import
# - pkgutil           # Package extension utility
# - modulefinder      # Find modules used by a script
# - runpy             # Locating and executing Python modules
# - ast               # Abstract syntax trees
# - dis               # Disassembler for Python bytecode
# - code              # Interpreter base classes
# - codeop            # Compile Python code
# - tokenize          # Tokenizer for Python source
# - keyword           # Testing for Python keywords
# - builtins          # Built-in objects
# - __future__        # Future statement definitions

# ============================================================================
# DEVELOPMENT DEPENDENCIES (Optional, for development workflow)
# ============================================================================
# Uncomment these for development and code quality tools:

# flake8>=6.0.0,<7.0.0             # Code style checker
# black>=23.0.0,<24.0.0            # Code formatter
# isort>=5.12.0,<6.0.0             # Import sorter
# bandit>=1.7.5,<2.0.0             # Security linter
# safety>=2.3.0,<3.0.0             # Dependency vulnerability scanner
# pre-commit>=3.3.0,<4.0.0         # Git pre-commit hooks

# ============================================================================
# PLATFORM-SPECIFIC NOTES
# ============================================================================
# - fcntl module is Unix/Linux only (not available on Windows)
# - Some SSL cipher suites may vary by platform
# - Performance characteristics may differ between platforms
# - File locking behavior is platform-dependent

# ============================================================================
# VERSION COMPATIBILITY
# ============================================================================
# Tested with:
# - Python 3.10+
# - Ubuntu 20.04+ / CentOS 8+ / Debian 11+
# - Windows 10+ (with limitations on fcntl functionality)
# - macOS 11+ (Big Sur and later)

# Minimum Python version: 3.10
# Recommended Python version: 3.11 or 3.12
