#!/usr/bin/env python3
"""
Centralized configuration loader for TCP String Search Server.

This module provides statically typed configuration loading using TypedDict
and configparser, following best practices for type safety and validation.
"""

import os
import configparser
from configparser import ConfigParser
from pathlib import Path
from typing import TypedDict, Optional


class DefaultConfig(TypedDict):
    """Configuration from DEFAULT section."""
    linuxpath: str
    reread_on_query: bool


class ServerConfig(TypedDict):
    """Configuration from server section."""
    host: str
    port: int
    max_connections: int
    max_payload_size: int
    connection_timeout: int
    tcp_nodelay: bool
    socket_buffer_size: int


class SSLConfig(TypedDict):
    """Configuration from ssl section."""
    ssl_enabled: bool
    ssl_cert_file: str
    ssl_key_file: str
    min_tls_version: str
    verify_client_cert: bool
    ca_file: str


class LoggingConfig(TypedDict):
    """Configuration from logging section."""
    log_level: str
    log_file: str
    thread_pool_size: int


class ConfigurationError(Exception):
    """Raised when configuration is invalid or missing."""
    pass


def _validate_boolean(value: str, param_name: str) -> bool:
    """
    Validate and convert boolean value.

    Args:
        value: String value to validate
        param_name: Parameter name for error messages

    Returns:
        Boolean value

    Raises:
        ConfigurationError: If value is not a valid boolean
    """
    # Accept boolean values (case-insensitive for compatibility)
    if value.lower() in ('true', 'false'):
        return value.lower() == 'true'
    # Also accept yes/no, 1/0 for compatibility
    elif value.lower() in ('yes', '1'):
        return True
    elif value.lower() in ('no', '0'):
        return False
    else:
        raise ConfigurationError(f"Invalid boolean value: {value}")


def find_config_file() -> str:
    """
    Find configuration file in standard locations.

    Returns:
        Path to the first config file found

    Raises:
        ConfigurationError: If no config file is found
    """
    config_locations = [
        "config.ini",
        "~/.local/tcp-string-search/config.ini"
    ]

    for location in config_locations:
        config_path = Path(location).expanduser()
        if config_path.exists():
            return str(config_path)

    raise ConfigurationError(
        f"No configuration file found in any of these locations: {config_locations}"
    )


def load_config(config_file: Optional[str] = None) -> tuple[DefaultConfig, ServerConfig, SSLConfig, LoggingConfig]:
    """
    Load configuration from config.ini file with proper static typing.

    Args:
        config_file: Path to configuration file. If None, searches standard locations.

    Returns:
        Tuple containing (default_config, server_config, ssl_config, logging_config)

    Raises:
        ConfigurationError: If config file is invalid or missing required parameters
    """
    if config_file is None:
        config_file = find_config_file()
    
    if not os.path.exists(config_file):
        raise ConfigurationError("Configuration file not found")

    parser = ConfigParser()
    
    try:
        parser.read(config_file)
    except Exception as e:
        raise ConfigurationError(f"Failed to parse configuration file: {e}")

    # Load DEFAULT configuration with error handling
    try:
        linuxpath = parser.get("DEFAULT", "linuxpath")
        reread_on_query_str = parser.get("DEFAULT", "reread_on_query")
        reread_on_query = _validate_boolean(reread_on_query_str, "reread_on_query")

        default_config: DefaultConfig = {
            "linuxpath": linuxpath,
            "reread_on_query": reread_on_query,
        }
    except (configparser.NoOptionError, ValueError) as e:
        if "linuxpath" in str(e):
            raise ConfigurationError("Missing required parameter: linuxpath")
        if "Not a boolean" in str(e):
            # Extract the actual value from the error message
            value = str(e).split(': ')[1] if ': ' in str(e) else str(e)
            raise ConfigurationError(f"Invalid boolean value: {value}")
        raise ConfigurationError(f"Missing required parameter: {e}")
    except ConfigurationError as e:
        if "Invalid boolean value" in str(e):
            raise
        raise ConfigurationError(f"Missing required parameter: {e}")

    # Load server configuration with validation
    try:
        if 'server' not in parser:
            raise ConfigurationError("Missing required parameter: server section not found")

        server_section = parser['server']

        # Validate required parameters
        if 'host' not in server_section:
            raise ConfigurationError("Missing required parameter: host not found in server section")
        if 'port' not in server_section:
            raise ConfigurationError("Missing required parameter: port not found in server section")

        host = server_section.get("host")
        if not host:
            raise ConfigurationError("Missing required parameter: host cannot be empty")

        port = server_section.getint("port")
        if port is None:
            raise ConfigurationError("Missing required parameter: port cannot be None")
        if port < 0 or port > 65535:
            raise ConfigurationError(f"Port must be between 0 and 65535, got {port}")

        server_config: ServerConfig = {
            "host": host,
            "port": port,
            "max_connections": server_section.getint("max_connections", 200),
            "max_payload_size": server_section.getint("max_payload_size", 1024),
            "connection_timeout": server_section.getint("connection_timeout", 5),
            "tcp_nodelay": server_section.getboolean("tcp_nodelay", True),
            "socket_buffer_size": server_section.getint("socket_buffer_size", 262144),
        }
    except (configparser.NoOptionError, ValueError) as e:
        if "invalid literal for int()" in str(e):
            raise ConfigurationError(f"Invalid integer value: {e}")
        raise ConfigurationError(f"Missing required parameter: {e}")
    except ConfigurationError:
        raise

    # Load SSL configuration with validation
    # Resolve certificate paths relative to config directory
    config_dir = Path(config_file).parent.resolve()

    def resolve_cert_path(cert_path: str) -> str:
        if not cert_path:
            return cert_path
        cert_path_obj = Path(cert_path)
        if cert_path_obj.is_absolute():
            return str(cert_path_obj)
        else:
            return str(config_dir / cert_path)

    if 'ssl' not in parser:
        # Create default SSL configuration if section is missing
        ssl_config: SSLConfig = {
            "ssl_enabled": False,
            "ssl_cert_file": resolve_cert_path("certs/server.crt"),
            "ssl_key_file": resolve_cert_path("certs/server.key"),
            "min_tls_version": "TLSv1.3",
            "verify_client_cert": False,
            "ca_file": resolve_cert_path("certs/ca.crt"),
        }
    else:
        ssl_section = parser['ssl']
        ssl_config = {
            "ssl_enabled": ssl_section.getboolean("ssl_enabled", False),
            "ssl_cert_file": resolve_cert_path(ssl_section.get("ssl_cert_file", "certs/server.crt")),
            "ssl_key_file": resolve_cert_path(ssl_section.get("ssl_key_file", "certs/server.key")),
            "min_tls_version": ssl_section.get("min_tls_version", "TLSv1.3"),
            "verify_client_cert": ssl_section.getboolean("verify_client_cert", False),
            "ca_file": resolve_cert_path(ssl_section.get("ca_file", "certs/ca.crt")),
        }

    # Load logging configuration with validation
    if 'logging' not in parser:
        # Create default logging configuration if section is missing
        logging_config: LoggingConfig = {
            "log_level": "DEBUG",
            "log_file": "logs/server.log",
            "thread_pool_size": 50,
        }
    else:
        logging_section = parser['logging']
        logging_config = {
            "log_level": logging_section.get("log_level", "DEBUG"),
            "log_file": logging_section.get("log_file", "logs/server.log"),
            "thread_pool_size": logging_section.getint("thread_pool_size", 50),
        }

    # Validate that the file specified by linuxpath exists
    file_path = default_config["linuxpath"]
    if not Path(file_path).is_absolute():
        file_path = str(config_dir / file_path)
    
    if not os.path.exists(file_path):
        raise ConfigurationError(f"File specified by linuxpath does not exist: {file_path}")

    return default_config, server_config, ssl_config, logging_config


def get_file_path(config_file: Optional[str] = None) -> str:
    """
    Get the resolved file path from configuration.

    Args:
        config_file: Path to configuration file

    Returns:
        Absolute path to the file specified by linuxpath
    """
    default_config, _, _, _ = load_config(config_file)
    
    if config_file is None:
        config_file = find_config_file()
    
    config_dir = Path(config_file).parent.resolve()
    file_path = default_config["linuxpath"]
    
    if not Path(file_path).is_absolute():
        file_path = str(config_dir / file_path)
    
    return file_path
