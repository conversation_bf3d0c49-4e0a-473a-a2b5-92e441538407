#!/usr/bin/env python3
"""
SSL Certificate Generator for TCP String Search Server

Generates self-signed SSL certificates for secure server communication.
Creates CA certificate, server certificate, and client certificate for testing.

Author: <PERSON>
Date: 2025
"""

import os
import subprocess
import sys
from pathlib import Path
from typing import Optional, List


def run_command(cmd: list[str], description: str) -> bool:
    """
    Run a shell command and handle errors.
    
    Args:
        cmd: Command and arguments as list
        description: Description of what the command does
        
    Returns:
        True if successful, False otherwise
    """
    try:
        print(f"Generating {description}...")
        result = subprocess.run(cmd, capture_output=True, text=True, check=True)
        print(f"✓ {description} generated successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to generate {description}")
        print(f"Error: {e.stderr}")
        return False
    except FileNotFoundError:
        print(f"✗ OpenSSL not found. Please install OpenSSL to generate certificates.")
        return False


def create_cert_directory() -> Path:
    """
    Create certificates directory if it doesn't exist.
    
    Returns:
        Path to certificates directory
    """
    cert_dir = Path("certs")
    cert_dir.mkdir(exist_ok=True)
    print(f"✓ Certificate directory: {cert_dir.absolute()}")
    return cert_dir


def generate_ca_certificate(cert_dir: Path) -> bool:
    """
    Generate Certificate Authority (CA) certificate.
    
    Args:
        cert_dir: Directory to store certificates
        
    Returns:
        True if successful, False otherwise
    """
    ca_key = cert_dir / "ca.key"
    ca_crt = cert_dir / "ca.crt"
    
    # Generate CA private key
    cmd_key = [
        "openssl", "genrsa", "-out", str(ca_key), "4096"
    ]
    
    if not run_command(cmd_key, "CA private key"):
        return False
    
    # Generate CA certificate
    cmd_cert = [
        "openssl", "req", "-new", "-x509", "-days", "365", "-key", str(ca_key),
        "-out", str(ca_crt), "-subj",
        "/C=US/ST=State/L=City/O=TCP-String-Search/OU=CA/CN=TCP-String-Search-CA"
    ]
    
    return run_command(cmd_cert, "CA certificate")


def generate_server_certificate(cert_dir: Path, common_name: str = "localhost",
                               dns_names: Optional[List[str]] = None,
                               ip_addresses: Optional[List[str]] = None) -> bool:
    """
    Generate server certificate signed by CA.

    Args:
        cert_dir: Directory to store certificates
        common_name: Common name for the certificate
        dns_names: List of DNS names for SAN
        ip_addresses: List of IP addresses for SAN

    Returns:
        True if successful, False otherwise
    """
    if dns_names is None:
        dns_names = ["localhost", "*.localhost"]
    if ip_addresses is None:
        ip_addresses = ["127.0.0.1", "::1"]

    server_key = cert_dir / "server.key"
    server_csr = cert_dir / "server.csr"
    server_crt = cert_dir / "server.crt"
    ca_key = cert_dir / "ca.key"
    ca_crt = cert_dir / "ca.crt"

    # Generate server private key
    cmd_key = [
        "openssl", "genrsa", "-out", str(server_key), "2048"
    ]

    if not run_command(cmd_key, "server private key"):
        return False

    # Generate server certificate signing request
    cmd_csr = [
        "openssl", "req", "-new", "-key", str(server_key), "-out", str(server_csr),
        "-subj", f"/C=US/ST=State/L=City/O=TCP-String-Search/OU=Server/CN={common_name}"
    ]

    if not run_command(cmd_csr, "server certificate signing request"):
        return False

    # Create server extensions file with proper format
    ext_file = cert_dir / "server.ext"
    with open(ext_file, 'w') as f:
        f.write("authorityKeyIdentifier=keyid,issuer\n")
        f.write("basicConstraints=CA:FALSE\n")
        f.write("keyUsage=digitalSignature,nonRepudiation,keyEncipherment,dataEncipherment\n")
        f.write("subjectAltName=@alt_names\n")
        f.write("\n[alt_names]\n")

        # Add DNS names
        for i, dns_name in enumerate(dns_names, 1):
            f.write(f"DNS.{i} = {dns_name}\n")

        # Add IP addresses
        for i, ip_addr in enumerate(ip_addresses, 1):
            f.write(f"IP.{i} = {ip_addr}\n")

    # Generate server certificate signed by CA (without -extensions v3_req)
    cmd_cert = [
        "openssl", "x509", "-req", "-in", str(server_csr), "-CA", str(ca_crt),
        "-CAkey", str(ca_key), "-CAcreateserial", "-out", str(server_crt),
        "-days", "365", "-extfile", str(ext_file)
    ]

    success = run_command(cmd_cert, "server certificate")

    # Clean up temporary files
    server_csr.unlink(missing_ok=True)
    ext_file.unlink(missing_ok=True)

    return success


def generate_client_certificate(cert_dir: Path) -> bool:
    """
    Generate client certificate for testing (optional).
    
    Args:
        cert_dir: Directory to store certificates
        
    Returns:
        True if successful, False otherwise
    """
    client_key = cert_dir / "client.key"
    client_csr = cert_dir / "client.csr"
    client_crt = cert_dir / "client.crt"
    ca_key = cert_dir / "ca.key"
    ca_crt = cert_dir / "ca.crt"
    
    # Generate client private key
    cmd_key = [
        "openssl", "genrsa", "-out", str(client_key), "2048"
    ]
    
    if not run_command(cmd_key, "client private key"):
        return False
    
    # Generate client certificate signing request
    cmd_csr = [
        "openssl", "req", "-new", "-key", str(client_key), "-out", str(client_csr),
        "-subj", "/C=US/ST=State/L=City/O=TCP-String-Search/OU=Client/CN=client"
    ]
    
    if not run_command(cmd_csr, "client certificate signing request"):
        return False
    
    # Generate client certificate signed by CA
    cmd_cert = [
        "openssl", "x509", "-req", "-in", str(client_csr), "-CA", str(ca_crt),
        "-CAkey", str(ca_key), "-CAcreateserial", "-out", str(client_crt),
        "-days", "365"
    ]
    
    success = run_command(cmd_cert, "client certificate")
    
    # Clean up temporary files
    client_csr.unlink(missing_ok=True)
    
    return success


def set_permissions(cert_dir: Path) -> None:
    """
    Set appropriate permissions on certificate files.
    
    Args:
        cert_dir: Directory containing certificates
    """
    try:
        # Set directory permissions
        os.chmod(cert_dir, 0o700)
        
        # Set file permissions
        for cert_file in cert_dir.glob("*"):
            if cert_file.suffix == ".key":
                os.chmod(cert_file, 0o600)  # Private keys - read only by owner
            else:
                os.chmod(cert_file, 0o644)  # Certificates - readable by group
        
        print("✓ Certificate permissions set")
    except Exception as e:
        print(f"⚠ Warning: Could not set permissions: {e}")


def verify_certificates(cert_dir: Path) -> bool:
    """
    Verify generated certificates.
    
    Args:
        cert_dir: Directory containing certificates
        
    Returns:
        True if verification successful, False otherwise
    """
    server_crt = cert_dir / "server.crt"
    ca_crt = cert_dir / "ca.crt"
    
    if not server_crt.exists() or not ca_crt.exists():
        print("✗ Certificate files missing")
        return False
    
    # Verify server certificate against CA
    cmd_verify = [
        "openssl", "verify", "-CAfile", str(ca_crt), str(server_crt)
    ]
    
    return run_command(cmd_verify, "certificate verification")


def main() -> None:
    """
    Main certificate generation function.
    """
    import argparse

    # Parse command-line arguments
    parser = argparse.ArgumentParser(description="Generate SSL certificates for TCP String Search Server")
    parser.add_argument("--common-name", default="localhost", help="Common name for server certificate")
    parser.add_argument("--dns-names", nargs="*", default=["localhost", "*.localhost"],
                       help="DNS names for Subject Alternative Name")
    parser.add_argument("--ip-addresses", nargs="*", default=["127.0.0.1", "::1"],
                       help="IP addresses for Subject Alternative Name")
    parser.add_argument("--days", type=int, default=365, help="Certificate validity in days")
    parser.add_argument("--key-size", type=int, default=2048, help="RSA key size")

    args = parser.parse_args()

    print("TCP String Search Server - SSL Certificate Generator")
    print("=" * 55)
    print()

    # Check if OpenSSL is available
    try:
        subprocess.run(["openssl", "version"], capture_output=True, check=True)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("✗ OpenSSL is required but not found.")
        print("Please install OpenSSL:")
        print("  Ubuntu/Debian: sudo apt-get install openssl")
        print("  CentOS/RHEL: sudo yum install openssl")
        print("  macOS: brew install openssl")
        sys.exit(1)

    # Create certificate directory
    cert_dir = create_cert_directory()

    # Check if certificates already exist
    server_crt = cert_dir / "server.crt"
    if server_crt.exists():
        response = input("Certificates already exist. Regenerate? (y/N): ").strip().lower()
        if response not in ['y', 'yes']:
            print("Certificate generation cancelled.")
            return

    success = True

    # Generate certificates
    success &= generate_ca_certificate(cert_dir)
    success &= generate_server_certificate(cert_dir, args.common_name, args.dns_names, args.ip_addresses)
    success &= generate_client_certificate(cert_dir)
    
    if success:
        set_permissions(cert_dir)
        success &= verify_certificates(cert_dir)
    
    print()
    if success:
        print("✓ All certificates generated successfully!")
        print()
        print("Generated files:")
        print(f"  CA Certificate: {cert_dir}/ca.crt")
        print(f"  CA Private Key: {cert_dir}/ca.key")
        print(f"  Server Certificate: {cert_dir}/server.crt")
        print(f"  Server Private Key: {cert_dir}/server.key")
        print(f"  Client Certificate: {cert_dir}/client.crt")
        print(f"  Client Private Key: {cert_dir}/client.key")
        print()
        print("To use SSL, set ssl_enabled = True in config.ini")
    else:
        print("✗ Certificate generation failed!")
        sys.exit(1)


if __name__ == "__main__":
    main()
