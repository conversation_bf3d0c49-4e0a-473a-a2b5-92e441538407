"""
Test Suite for TCP String Search Server

This package contains comprehensive unit tests for all components of the
TCP String Search Server project, ensuring robust functionality, performance,
and error handling across all modules.

Test Modules:
- test_server.py: Tests for server components (config, search engine, handlers)
- test_client.py: Tests for client functionality and SSL connections
- test_search_algorithms.py: Tests for all search algorithm implementations
- test_search.py: Tests for FileSearchEngine class
- test_config.py: Tests for ServerConfig class
- test_generate_speed_report.py: Tests for performance reporting
- test_hashset.py: Performance tests for HashSetSearch algorithm

Coverage:
The test suite aims for 100% code coverage and includes:
- Unit tests for all classes and functions
- Integration tests for client-server communication
- Performance tests for search algorithms
- Error handling and edge case testing
- SSL/TLS functionality testing
- Configuration validation testing

Author: <PERSON>
Date: 2025
"""