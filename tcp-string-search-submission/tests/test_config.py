#!/usr/bin/env python3
"""
Comprehensive Test Suite for ServerConfig Class

This module contains unit tests for the ServerConfig class, ensuring
proper configuration file loading, validation, and error handling.

Test Coverage:
- Configuration file loading and parsing
- Parameter type validation (string, int, boolean)
- Required parameter validation
- Error handling for missing or invalid files
- Section-based parameter access

Author: <PERSON>
Date: 2025
"""

import pytest
from config_loader import load_config, ConfigurationError


def test_config_loading():
    """Test basic configuration loading and parameter type validation."""
    default_config, server_config, ssl_config, logging_config = load_config()
    assert default_config["linuxpath"] is not None
    assert isinstance(server_config['host'], str)
    assert isinstance(server_config['port'], int)
    assert isinstance(default_config['reread_on_query'], bool)
    assert isinstance(ssl_config['ssl_enabled'], bool)
    assert isinstance(logging_config['log_level'], str)

def test_invalid_config_file():
    """Test handling of invalid or non-existent config file."""
    with pytest.raises(ConfigurationError):
        load_config("nonexistent.ini")

def test_required_parameters():
    """Test validation of required parameters across different config sections."""
    default_config, server_config, ssl_config, logging_config = load_config()
    # Test required parameters from DEFAULT section
    assert default_config['linuxpath'] is not None
    assert isinstance(default_config['reread_on_query'], bool)

    # Test required parameters from server section
    assert server_config['host'] is not None
    assert server_config['port'] > 0
    assert server_config['max_connections'] > 0
