#!/usr/bin/env python3
"""
Comprehensive Test Suite for Search Algorithms

This module provides exhaustive testing for all search algorithm implementations,
ensuring 100% code coverage, performance validation, and robust error handling.

Test Coverage:
- HashSetSearch with all configurations
- LinearSearch with optimizations
- BinarySearch with deduplication options
- MMapSearch with memory mapping
- GrepSearch with system command integration
- Performance comparisons and benchmarks
- Edge cases and boundary conditions
- Error handling and exception scenarios
- Memory usage validation
- Unicode and encoding handling

Author: <PERSON>
Date: 2025
"""

import os
import sys
import tempfile
import time
import pytest
from pathlib import Path
from unittest.mock import patch, Mock

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from search_algorithms_new import (
    HashSetSearch, LinearSearch, BinarySearch, 
    MMapSearch, GrepSearch
)


class TestHashSetSearchAlgorithm:
    """Comprehensive tests for HashSetSearch algorithm."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)

    @pytest.fixture
    def test_file(self, temp_dir: Path):
        """Create a test file with known content."""
        test_file = temp_dir / "test.txt"
        content = "line1\nline2\nline3\nduplicate\nduplicate\nspecial chars: àáâã\nempty line below\n\nlast line\n"
        test_file.write_text(content, encoding='utf-8')
        return test_file

    def test_hashset_search_initialization_default(self):
        """Test HashSetSearch initialization with default parameters."""
        searcher = HashSetSearch()
        
        assert searcher.use_frozenset is True  # Default
        assert searcher.reread_on_query is False  # Default
        assert searcher.lines is None
        assert searcher.file_path is None

    def test_hashset_search_initialization_custom(self):
        """Test HashSetSearch initialization with custom parameters."""
        searcher = HashSetSearch(use_frozenset=False, reread_on_query=True)
        
        assert searcher.use_frozenset is False
        assert searcher.reread_on_query is True
        assert searcher.lines is None
        assert searcher.file_path is None

    def test_hashset_search_load_file(self, test_file: Path):
        """Test loading a file into HashSetSearch."""
        searcher = HashSetSearch()
        searcher.load(str(test_file))
        
        assert searcher.file_path == str(test_file)
        assert searcher.lines is not None
        assert isinstance(searcher.lines, (set, frozenset))
        assert "line1" in searcher.lines
        assert "line2" in searcher.lines
        assert "line3" in searcher.lines

    def test_hashset_search_with_frozenset(self, test_file: Path):
        """Test HashSetSearch using frozenset."""
        searcher = HashSetSearch(use_frozenset=True)
        searcher.load(str(test_file))
        
        assert isinstance(searcher.lines, frozenset)

    def test_hashset_search_with_regular_set(self, test_file: Path):
        """Test HashSetSearch using regular set."""
        searcher = HashSetSearch(use_frozenset=False)
        searcher.load(str(test_file))
        
        assert isinstance(searcher.lines, set)

    def test_hashset_search_existing_line(self, test_file: Path):
        """Test searching for existing lines."""
        searcher = HashSetSearch()
        searcher.load(str(test_file))
        
        assert searcher.search("line1") is True
        assert searcher.search("line2") is True
        assert searcher.search("line3") is True
        assert searcher.search("duplicate") is True
        assert searcher.search("special chars: àáâã") is True
        assert searcher.search("") is True  # Empty line exists
        assert searcher.search("last line") is True

    def test_hashset_search_nonexistent_line(self, test_file: Path):
        """Test searching for non-existent lines."""
        searcher = HashSetSearch()
        searcher.load(str(test_file))
        
        assert searcher.search("nonexistent") is False
        assert searcher.search("LINE1") is False  # Case sensitive
        assert searcher.search("line1 ") is False  # Trailing space
        assert searcher.search(" line1") is False  # Leading space
        assert searcher.search("partial") is False

    def test_hashset_search_reread_mode(self, test_file: Path):
        """Test HashSetSearch in reread mode."""
        searcher = HashSetSearch(reread_on_query=True)
        
        # In reread mode, we don't load the file initially
        assert searcher.search("line1", str(test_file)) is True
        assert searcher.search("nonexistent", str(test_file)) is False

    def test_hashset_search_fast_load_method(self, test_file: Path):
        """Test the fast_load method for reread mode."""
        searcher = HashSetSearch(reread_on_query=True)
        
        result = searcher.fast_load(str(test_file), "line1")
        assert result is True
        
        result = searcher.fast_load(str(test_file), "nonexistent")
        assert result is False

    def test_hashset_search_performance_cached_mode(self, test_file: Path):
        """Test performance of HashSetSearch in cached mode."""
        searcher = HashSetSearch(reread_on_query=False)
        searcher.load(str(test_file))
        
        # Measure search time
        start_time = time.perf_counter()
        result = searcher.search("line1")
        end_time = time.perf_counter()
        
        execution_time_ms = (end_time - start_time) * 1000
        
        assert result is True
        assert execution_time_ms < 0.5  # Should be very fast

    def test_hashset_search_performance_reread_mode(self, test_file: Path):
        """Test performance of HashSetSearch in reread mode."""
        searcher = HashSetSearch(reread_on_query=True)
        
        # Measure search time
        start_time = time.perf_counter()
        result = searcher.search("line1", str(test_file))
        end_time = time.perf_counter()
        
        execution_time_ms = (end_time - start_time) * 1000
        
        assert result is True
        assert execution_time_ms < 40.0  # Should meet requirement

    def test_hashset_search_name_method(self):
        """Test the name method returns correct algorithm name."""
        # Test default configuration
        searcher1 = HashSetSearch()
        assert "Hash Set" in searcher1.name()
        assert "FrozenSet" in searcher1.name()
        
        # Test with regular set
        searcher2 = HashSetSearch(use_frozenset=False)
        assert "Hash Set" in searcher2.name()
        assert "FrozenSet" not in searcher2.name()
        
        # Test with reread mode
        searcher3 = HashSetSearch(reread_on_query=True)
        assert "Hash Set" in searcher3.name()
        assert "Reread" in searcher3.name()

    def test_hashset_search_unicode_handling(self, temp_dir: Path):
        """Test HashSetSearch with Unicode content."""
        unicode_file = temp_dir / "unicode.txt"
        unicode_content = "English line\n中文行\nрусская строка\n🚀 emoji line\nمرحبا\n"
        unicode_file.write_text(unicode_content, encoding='utf-8')
        
        searcher = HashSetSearch()
        searcher.load(str(unicode_file))
        
        assert searcher.search("English line") is True
        assert searcher.search("中文行") is True
        assert searcher.search("русская строка") is True
        assert searcher.search("🚀 emoji line") is True
        assert searcher.search("مرحبا") is True

    def test_hashset_search_large_file_handling(self, temp_dir: Path):
        """Test HashSetSearch with a larger file."""
        large_file = temp_dir / "large.txt"
        
        # Create a file with 1000 lines
        lines = [f"line_{i:04d}" for i in range(1000)]
        large_file.write_text("\n".join(lines) + "\n")
        
        searcher = HashSetSearch()
        searcher.load(str(large_file))
        
        # Test searching for various lines
        assert searcher.search("line_0000") is True
        assert searcher.search("line_0500") is True
        assert searcher.search("line_0999") is True
        assert searcher.search("line_1000") is False  # Doesn't exist

    def test_hashset_search_empty_file(self, temp_dir: Path):
        """Test HashSetSearch with an empty file."""
        empty_file = temp_dir / "empty.txt"
        empty_file.write_text("")
        
        searcher = HashSetSearch()
        searcher.load(str(empty_file))
        
        assert searcher.search("anything") is False
        assert searcher.search("") is False

    def test_hashset_search_file_with_only_newlines(self, temp_dir: Path):
        """Test HashSetSearch with a file containing only newlines."""
        newlines_file = temp_dir / "newlines.txt"
        newlines_file.write_text("\n\n\n")
        
        searcher = HashSetSearch()
        searcher.load(str(newlines_file))
        
        assert searcher.search("") is True  # Empty lines exist
        assert searcher.search("anything") is False

    def test_hashset_search_file_without_final_newline(self, temp_dir: Path):
        """Test HashSetSearch with a file that doesn't end with newline."""
        no_newline_file = temp_dir / "no_newline.txt"
        no_newline_file.write_text("line1\nline2\nline3")  # No final newline
        
        searcher = HashSetSearch()
        searcher.load(str(no_newline_file))
        
        assert searcher.search("line1") is True
        assert searcher.search("line2") is True
        assert searcher.search("line3") is True

    def test_hashset_search_duplicate_lines(self, test_file: Path):
        """Test HashSetSearch behavior with duplicate lines."""
        searcher = HashSetSearch()
        searcher.load(str(test_file))
        
        # The file contains "duplicate" twice, but set should only store it once
        assert searcher.search("duplicate") is True
        
        # Verify that the set contains unique lines only
        if searcher.lines is not None:
            duplicate_count = sum(1 for line in searcher.lines if line == "duplicate")
            assert duplicate_count == 1  # Should only appear once in the set


class TestLinearSearchAlgorithm:
    """Comprehensive tests for LinearSearch algorithm."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)

    @pytest.fixture
    def test_file(self, temp_dir: Path):
        """Create a test file with known content."""
        test_file = temp_dir / "test.txt"
        content = "line1\nline2\nline3\nduplicate\nduplicate\nspecial chars: àáâã\n"
        test_file.write_text(content, encoding='utf-8')
        return test_file

    def test_linear_search_initialization(self):
        """Test LinearSearch initialization."""
        searcher = LinearSearch()
        
        assert searcher.file_path is None

    def test_linear_search_load_file(self, test_file: Path):
        """Test loading a file into LinearSearch."""
        searcher = LinearSearch()
        searcher.load(str(test_file))
        
        assert searcher.file_path == str(test_file)

    def test_linear_search_existing_line(self, test_file: Path):
        """Test searching for existing lines."""
        searcher = LinearSearch()
        searcher.load(str(test_file))
        
        assert searcher.search("line1") is True
        assert searcher.search("line2") is True
        assert searcher.search("line3") is True
        assert searcher.search("duplicate") is True
        assert searcher.search("special chars: àáâã") is True

    def test_linear_search_nonexistent_line(self, test_file: Path):
        """Test searching for non-existent lines."""
        searcher = LinearSearch()
        searcher.load(str(test_file))
        
        assert searcher.search("nonexistent") is False
        assert searcher.search("LINE1") is False  # Case sensitive
        assert searcher.search("partial") is False

    def test_linear_search_name_method(self):
        """Test the name method returns correct algorithm name."""
        searcher = LinearSearch()
        assert "Linear Search" in searcher.name()

    def test_linear_search_performance(self, test_file: Path):
        """Test LinearSearch performance."""
        searcher = LinearSearch()
        searcher.load(str(test_file))
        
        # Measure search time
        start_time = time.perf_counter()
        result = searcher.search("line1")
        end_time = time.perf_counter()
        
        execution_time_ms = (end_time - start_time) * 1000
        
        assert result is True
        # Linear search should be reasonably fast for small files
        assert execution_time_ms < 10.0

    def test_linear_search_unicode_handling(self, temp_dir: Path):
        """Test LinearSearch with Unicode content."""
        unicode_file = temp_dir / "unicode.txt"
        unicode_content = "English line\n中文行\nрусская строка\n🚀 emoji line\n"
        unicode_file.write_text(unicode_content, encoding='utf-8')
        
        searcher = LinearSearch()
        searcher.load(str(unicode_file))
        
        assert searcher.search("English line") is True
        assert searcher.search("中文行") is True
        assert searcher.search("русская строка") is True
        assert searcher.search("🚀 emoji line") is True

    def test_linear_search_empty_file(self, temp_dir: Path):
        """Test LinearSearch with an empty file."""
        empty_file = temp_dir / "empty.txt"
        empty_file.write_text("")
        
        searcher = LinearSearch()
        searcher.load(str(empty_file))
        
        assert searcher.search("anything") is False
        assert searcher.search("") is False


class TestBinarySearchAlgorithm:
    """Comprehensive tests for BinarySearch algorithm."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)

    @pytest.fixture
    def test_file(self, temp_dir: Path):
        """Create a test file with known content."""
        test_file = temp_dir / "test.txt"
        content = "zebra\napple\nbanana\napple\ncherry\n"  # Unsorted with duplicates
        test_file.write_text(content, encoding='utf-8')
        return test_file

    def test_binary_search_initialization_default(self):
        """Test BinarySearch initialization with default parameters."""
        searcher = BinarySearch()
        
        assert searcher.deduplicate is True  # Default
        assert searcher.lines is None
        assert searcher.file_path is None

    def test_binary_search_initialization_custom(self):
        """Test BinarySearch initialization with custom parameters."""
        searcher = BinarySearch(deduplicate=False)
        
        assert searcher.deduplicate is False

    def test_binary_search_load_file_with_deduplication(self, test_file: Path):
        """Test loading a file into BinarySearch with deduplication."""
        searcher = BinarySearch(deduplicate=True)
        searcher.load(str(test_file))

        assert searcher.lines is not None
        assert isinstance(searcher.lines, list)

        # Lines should be sorted and deduplicated
        assert "apple" in searcher.lines
        assert "banana" in searcher.lines
        assert "cherry" in searcher.lines
        assert "zebra" in searcher.lines

        # Should be sorted
        assert searcher.lines == sorted(set(["zebra", "apple", "banana", "cherry"]))

    def test_binary_search_load_file_without_deduplication(self, test_file: Path):
        """Test loading a file into BinarySearch without deduplication."""
        searcher = BinarySearch(deduplicate=False)
        searcher.load(str(test_file))
        
        # Lines should be sorted but not deduplicated
        expected_lines = sorted(["zebra", "apple", "banana", "apple", "cherry"])
        assert searcher.lines == expected_lines

    def test_binary_search_existing_line(self, test_file: Path):
        """Test searching for existing lines."""
        searcher = BinarySearch()
        searcher.load(str(test_file))
        
        assert searcher.search("apple") is True
        assert searcher.search("banana") is True
        assert searcher.search("cherry") is True
        assert searcher.search("zebra") is True

    def test_binary_search_nonexistent_line(self, test_file: Path):
        """Test searching for non-existent lines."""
        searcher = BinarySearch()
        searcher.load(str(test_file))
        
        assert searcher.search("nonexistent") is False
        assert searcher.search("APPLE") is False  # Case sensitive
        assert searcher.search("partial") is False

    def test_binary_search_name_method(self):
        """Test the name method returns correct algorithm name."""
        searcher1 = BinarySearch(deduplicate=True)
        assert "Binary Search (Deduplicated)" in searcher1.name()
        
        searcher2 = BinarySearch(deduplicate=False)
        assert "Binary Search" in searcher2.name()
        assert "Deduplicated" not in searcher2.name()

    def test_binary_search_performance(self, test_file: Path):
        """Test BinarySearch performance."""
        searcher = BinarySearch()
        searcher.load(str(test_file))
        
        # Measure search time
        start_time = time.perf_counter()
        result = searcher.search("apple")
        end_time = time.perf_counter()
        
        execution_time_ms = (end_time - start_time) * 1000
        
        assert result is True
        # Binary search should be very fast
        assert execution_time_ms < 1.0
