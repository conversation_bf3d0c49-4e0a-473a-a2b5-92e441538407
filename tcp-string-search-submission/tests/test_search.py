#!/usr/bin/env python3
"""
Comprehensive Test Suite for FileSearchEngine Class

This module contains unit tests for the FileSearchEngine class, ensuring
proper file search functionality in both cached and reread modes.

Test Coverage:
- File loading and search operations
- Cached vs reread search modes
- File modification handling
- Error handling for missing files
- Unicode content support
- Empty file handling
- Large file performance

Author: <PERSON>
Date: 2025
"""

import os
import pytest
import tempfile
from server import FileSearchEngine, FileSearchError


@pytest.fixture
def test_file():
    """
    Create a temporary test file with sample content.

    Creates a temporary file with four lines of test content
    and automatically cleans it up after the test completes.

    Yields:
        str: Path to the temporary test file
    """
    with tempfile.NamedTemporaryFile(mode='w', delete=False) as f:
        f.write("line one\n")
        f.write("line two\n")
        f.write("test line\n")
        f.write("final line\n")
    yield f.name
    os.unlink(f.name)

def test_file_not_found():
    """Test proper error handling when attempting to load non-existent file."""
    with pytest.raises(FileSearchError):
        FileSearchEngine("nonexistent.txt")

def test_search_cached(test_file: str):
    """Test search functionality in cached mode (reread_on_query=False)."""
    engine = FileSearchEngine(test_file, reread_on_query=False)
    assert engine.search("line one") is True
    assert engine.search("line two") is True
    assert engine.search("nonexistent line") is False

def test_search_reread(test_file: str):
    """Test search functionality in reread mode (reread_on_query=True)."""
    engine = FileSearchEngine(test_file, reread_on_query=True)
    assert engine.search("line one") is True
    assert engine.search("line two") is True
    assert engine.search("nonexistent line") is False

def test_file_modification(test_file: str):
    """
    Test file modification handling in different search modes.

    Verifies that reread mode detects file changes while cached mode
    maintains its original loaded state.
    """
    engine = FileSearchEngine(test_file, reread_on_query=True)
    assert engine.search("new line") is False
    
    # Add new line to file
    with open(test_file, 'a') as f:
        f.write("new line\n")
    
    # Should find the new line when re-reading
    assert engine.search("new line") is True
    
    # Cached version should not find the new line
    cached_engine = FileSearchEngine(test_file, reread_on_query=False)
    cached_engine.search("line one")  # Initial cache load
    
    # Add another line
    with open(test_file, 'a') as f:
        f.write("another new line\n")
    
    # Cached version should not see the new line
    assert cached_engine.search("another new line") is False

def test_empty_file(test_file: str):
    """Test search behavior with empty files."""
    # Create empty file
    with open(test_file, 'w') as _:
        pass
    
    engine = FileSearchEngine(test_file)
    assert engine.search("any string") is False

def test_large_file():
    """Test search performance and functionality with large files."""
    file_path = "test_data/200k.txt"
    engine = FileSearchEngine(file_path)
    
    # Test first line
    with open(file_path, 'r') as f:
        first_line = f.readline().strip()
    assert engine.search(first_line) is True
    
    # Test random string that shouldn't exist
    assert engine.search("THIS STRING SHOULD NOT EXIST IN FILE 123!@#") is False

def test_unicode_handling(test_file: str):
    """
    Test proper handling of Unicode characters in file content.

    Verifies that the search engine correctly processes and finds
    Unicode strings including accented characters and non-Latin scripts.
    """
    test_strings = [
        "Hello, 世界!",
        "Café", 
        "über",
        "ñandú"
    ]
    
    # Write Unicode content
    with open(test_file, 'w', encoding='utf-8') as f:
        for s in test_strings:
            f.write(f"{s}\n")
    
    engine = FileSearchEngine(test_file)
    for s in test_strings:
        assert engine.search(s) is True