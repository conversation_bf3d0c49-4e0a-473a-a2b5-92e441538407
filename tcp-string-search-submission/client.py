#!/usr/bin/env python3
"""
TCP String Search Client

A client for testing the TCP String Search Server.
Supports both SSL and non-SSL connections with performance measurement.

Author: Interview Candidate
Date: 2024
"""

import socket
import ssl
import time
import argparse
import sys
import os
from pathlib import Path
from typing import Tuple, Optional, Union, Any

from config_loader import load_config as load_config_new, find_config_file, SSLConfig


class SearchClient:
    """
    TCP client for string search server communication.

    Supports both SSL and non-SSL connections with automatic
    performance measurement and error handling.
    """

    def __init__(self, host: str = 'localhost', port: int = 8888,
                 use_ssl: bool = False, timeout: float = 5.0,
                 cert_file: str | None = None, key_file: str | None = None,
                 ca_file: str | None = None, verify_cert: bool = True,
                 raw_mode: bool = False) -> None:
        """
        Initialize search client.

        Args:
            host: Server hostname or IP address
            port: Server port number
            use_ssl: Whether to use SSL connection
            timeout: Connection timeout in seconds
            cert_file: Path to client certificate file (optional)
            key_file: Path to client private key file (optional)
            ca_file: Path to CA certificate file for server verification (optional)
            verify_cert: Whether to verify server certificate (default True)
            raw_mode: Whether to use raw TCP mode (telnet-like behavior)
        """
        self.host = host
        self.port = port
        self.use_ssl = use_ssl
        self.timeout = timeout
        self.cert_file = cert_file
        self.key_file = key_file
        # Only set ca_file if explicitly provided
        self.ca_file = ca_file
        self.verify_cert = verify_cert
        self.raw_mode = raw_mode
        self._connection: Optional[Union[socket.socket, ssl.SSLSocket]] = None

    def _find_ca_file(self) -> str | None:
        """Find CA certificate file in standard locations."""
        ca_locations = [
            "certs/ca.crt",
            "~/.local/tcp-string-search/certs/ca.crt"
        ]

        for location in ca_locations:
            expanded_path = os.path.expanduser(location)
            if os.path.exists(expanded_path):
                return expanded_path
        return None

    def create_ssl_context(self) -> ssl.SSLContext:
        """Create SSL context for server-side SSL only."""
        # Create context that doesn't verify certificates for testing
        context = ssl.SSLContext(ssl.PROTOCOL_TLS_CLIENT)
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        context.minimum_version = ssl.TLSVersion.TLSv1_2

        # Set compatible cipher suites
        try:
            context.set_ciphers('ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-GCM-SHA256:AES256-GCM-SHA384:AES128-GCM-SHA256')
        except ssl.SSLError:
            # Fallback to default ciphers if custom ones fail
            pass

        # Only load client cert if both are provided (for future use)
        if self.cert_file and self.key_file and os.path.exists(self.cert_file) and os.path.exists(self.key_file):
            context.load_cert_chain(self.cert_file, self.key_file)

        return context

    def connect(self) -> None:
        """Establish persistent connection to server."""
        if self._connection is not None:
            return

        try:
            # Create socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)

            # Wrap with SSL if required
            if self.use_ssl:
                context = self.create_ssl_context()
                sock = context.wrap_socket(sock, server_hostname=self.host)

            # Connect to server
            sock.connect((self.host, self.port))
            self._connection = sock

        except socket.timeout:
            raise TimeoutError(f"Connection to {self.host}:{self.port} timed out")
        except socket.error as e:
            raise ConnectionError(f"Failed to connect to {self.host}:{self.port}: {e}")

    def disconnect(self) -> None:
        """Close persistent connection."""
        if self._connection is not None:
            try:
                self._connection.close()
            except:
                pass
            self._connection = None

    def send_query_persistent(self, query: str) -> str:
        """Send query using persistent connection and return raw response."""
        if self._connection is None:
            self.connect()

        if self._connection is None:
            raise ConnectionError("Failed to establish connection")

        try:
            # Send query (ensure it's within 1024 bytes)
            query_bytes = query.encode('utf-8')
            if len(query_bytes) > 1024:
                raise ValueError(f"Query too long: {len(query_bytes)} bytes (max 1024)")

            self._connection.sendall(query_bytes)

            # Receive response
            response = self._connection.recv(1024).decode('utf-8').strip()
            return response

        except (socket.timeout, socket.error) as e:
            # Connection lost, try to reconnect once
            self.disconnect()
            try:
                self.connect()
                if self._connection is not None:
                    self._connection.sendall(query_bytes)
                    response = self._connection.recv(1024).decode('utf-8').strip()
                    return response
                else:
                    raise ConnectionError("Reconnection failed")
            except Exception:
                raise ConnectionError(f"Connection lost and reconnection failed: {e}")

    def send_query(self, query: str) -> Tuple[str, float]:
        """
        Send search query to server and return result with timing.
        This is an alias for the search method to maintain compatibility.

        Args:
            query: String to search for

        Returns:
            Tuple of (response, execution_time_ms)

        Raises:
            ConnectionError: If connection to server fails
            TimeoutError: If operation times out
        """
        return self.search(query)

    def search(self, query: str) -> Tuple[str, float]:
        """
        Send search query to server and return result with timing.

        Args:
            query: String to search for

        Returns:
            Tuple of (response, execution_time_ms)

        Raises:
            ConnectionError: If connection to server fails
            TimeoutError: If operation times out
        """
        start_time = time.time()
        sock = None

        try:
            # Create socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(self.timeout)

            # Wrap with SSL if required
            if self.use_ssl:
                context = self.create_ssl_context()
                sock = context.wrap_socket(sock, server_hostname=self.host)

            # Connect to server
            sock.connect((self.host, self.port))

            # Send query (ensure it's within 1024 bytes)
            query_bytes = query.encode('utf-8')
            if len(query_bytes) > 1024:
                raise ValueError(f"Query too long: {len(query_bytes)} bytes (max 1024)")

            sock.sendall(query_bytes)

            # Receive response
            response = sock.recv(1024).decode('utf-8').strip()

            # Calculate execution time
            execution_time = (time.time() - start_time) * 1000

            return response, execution_time

        except socket.timeout:
            raise TimeoutError(f"Connection to {self.host}:{self.port} timed out")
        except socket.error as e:
            raise ConnectionError(f"Failed to connect to {self.host}:{self.port}: {e}")
        finally:
            if sock is not None:
                sock.close()

    def interactive_mode(self) -> None:
        """Run client in interactive mode with persistent connection."""
        try:
            self.connect()
            print(f"Connected to {self.host}:{self.port}")
            print("Enter queries (empty line to quit):")

            while True:
                try:
                    query = input("> ").strip()
                    if not query:
                        continue  # Don't quit on empty line, just continue

                    if query.lower() in ['quit', 'exit', 'bye']:
                        break

                    if self.raw_mode:
                        # Raw mode: just return the response without formatting
                        response = self.send_query_persistent(query)
                        print(response)
                    else:
                        # Standard mode: include timing and formatting
                        start_time = time.time()
                        response = self.send_query_persistent(query)
                        exec_time = (time.time() - start_time) * 1000
                        print(f"Response: {response}")
                        print(f"Execution time: {exec_time:.2f}ms")

                except KeyboardInterrupt:
                    print("\nExiting...")
                    break
                except Exception as e:
                    print(f"Error: {e}")
                    # Try to reconnect on error
                    try:
                        self.disconnect()
                        self.connect()
                        print("Reconnected. Try your query again.")
                    except:
                        print("Failed to reconnect. Exiting.")
                        break

        finally:
            self.disconnect()

    def test_connection(self) -> bool:
        """
        Test if server is reachable.

        Returns:
            True if server responds, False otherwise
        """
        try:
            _, _ = self.search("test_connection")
            return True
        except (ConnectionError, TimeoutError):
            return False


def load_config() -> dict[str, Any]:
    """
    Load configuration from config.ini file using the new configuration loader.

    Returns:
        Dictionary with configuration values for backward compatibility
    """
    try:
        default_config, server_config, ssl_config, logging_config = load_config_new()
        return {
            'host': server_config['host'],
            'port': server_config['port'],
            'ssl_enabled': ssl_config['ssl_enabled'],
            'ssl_cert_file': ssl_config['ssl_cert_file'],
            'ssl_key_file': ssl_config['ssl_key_file'],
            'ca_file': ssl_config['ca_file'],
            'config_dir': Path.cwd()  # Fallback
        }
    except Exception:
        # Return minimal defaults if config loading fails
        return {
            'host': 'localhost',
            'port': 8888,
            'ssl_enabled': False,
            'ssl_cert_file': '',
            'ssl_key_file': '',
            'ca_file': '',
            'config_dir': Path.cwd()
        }


def main() -> None:
    """
    Main client entry point with command-line interface.
    """
    # Load configuration first
    config = load_config()

    parser = argparse.ArgumentParser(description='TCP String Search Client')
    parser.add_argument('--host', default=config['host'],
                        help=f'Server hostname (default: {config["host"]})')
    parser.add_argument('--port', type=int, default=config['port'],
                        help=f'Server port (default: {config["port"]})')
    # SSL options - use mutually exclusive group to handle config defaults properly
    ssl_group = parser.add_mutually_exclusive_group()
    ssl_group.add_argument('--ssl', action='store_true', dest='use_ssl',
                          help='Force SSL connection')
    ssl_group.add_argument('--no-ssl', action='store_false', dest='use_ssl',
                          help='Force non-SSL connection')
    parser.set_defaults(use_ssl=config['ssl_enabled'])

    parser.add_argument('--cert', type=str, default=config.get('ssl_cert_file') or None,
                        help='Path to client certificate file')
    parser.add_argument('--key', type=str, default=config.get('ssl_key_file') or None,
                        help='Path to client private key file')
    parser.add_argument('--ca', type=str, default=config.get('ca_file') or None,
                        help='Path to CA certificate file for server verification')
    parser.add_argument('--no-verify', action='store_true',
                        help='Disable server certificate verification')
    parser.add_argument('--timeout', type=float, default=30.0,
                        help='Connection timeout in seconds (default: 30.0)')
    parser.add_argument('--query', type=str,
                        help='Single query to send')
    parser.add_argument('--interactive', action='store_true',
                        help='Interactive mode for multiple queries')
    parser.add_argument('--raw', action='store_true',
                        help='Raw mode: minimal output like telnet (no formatting)')
    parser.add_argument('--test', action='store_true',
                        help='Test connection to server')

    args = parser.parse_args()

    # Create client with SSL options if enabled
    client = SearchClient(
        host=args.host,
        port=args.port,
        use_ssl=args.use_ssl,
        timeout=args.timeout,
        cert_file=args.cert,
        key_file=args.key,
        ca_file=args.ca,
        verify_cert=not args.no_verify,
        raw_mode=args.raw
    )

    # Test connection if requested
    if args.test:
        print(f"Testing connection to {args.host}:{args.port}...")
        if client.test_connection():
            print("✓ Server is reachable")
            return
        else:
            print("✗ Server is not reachable")
            sys.exit(1)

    # Single query mode
    if args.query:
        try:
            if args.raw:
                # Raw mode: just connect and send query
                client.connect()
                response = client.send_query_persistent(args.query)
                print(response)
                client.disconnect()
            else:
                # Standard mode: include timing and formatting
                response, exec_time = client.search(args.query)
                print(f"Query: {args.query}")
                print(f"Response: {response}")
                print(f"Execution time: {exec_time:.2f}ms")
        except Exception as e:
            print(f"Error: {e}")
            sys.exit(1)
        return

    # Interactive mode
    if args.interactive:
        client.interactive_mode()
        return

    # Default: show help
    parser.print_help()


if __name__ == "__main__":
    main()
